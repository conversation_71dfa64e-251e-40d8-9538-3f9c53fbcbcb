import React, { useState, useRef, useCallback, useEffect } from 'react';
import { Typography, Table, Form, Input, Button, Card, Divider, Empty, Tooltip, Space, Slider, Switch } from 'antd';
import { 
  DeleteOutlined, 
  EditOutlined, 
  CopyOutlined, 
  ArrowUpOutlined, 
  ArrowDownOutlined,
  ZoomInOutlined,
  ZoomOutOutlined,
  BorderOutlined,
  DragOutlined,
  EyeOutlined,
  SettingOutlined,
  FullscreenOutlined
} from '@ant-design/icons';
import styled from 'styled-components';

const { Title, Text, Paragraph } = Typography;

// Styled components for enhanced preview area
const PreviewContainer = styled.div`
  position: relative;
  height: 100%;
  background: #f5f5f5;
  border-radius: 8px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
`;

const PreviewToolbar = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 16px;
  background: white;
  border-bottom: 1px solid #e8e8e8;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  z-index: 10;
`;

const CanvasContainer = styled.div`
  flex: 1;
  position: relative;
  overflow: auto;
  background: ${props => props.showGrid ? 
    `radial-gradient(circle, #ddd 1px, transparent 1px)` : 
    '#f5f5f5'
  };
  background-size: ${props => props.gridSize || 20}px ${props => props.gridSize || 20}px;
  background-position: ${props => props.gridOffset?.x || 0}px ${props => props.gridOffset?.y || 0}px;
`;

const Canvas = styled.div`
  min-height: 100%;
  min-width: 100%;
  position: relative;
  transform: scale(${props => props.zoom || 1});
  transform-origin: top left;
  transition: transform 0.2s ease;
  padding: ${props => props.previewMode ? '0' : '32px'};
`;

const ComponentWrapper = styled.div`
  position: relative;
  margin: 8px 0;
  border: ${props => props.isSelected ? '2px solid #1890ff' : '1px dashed transparent'};
  border-radius: 4px;
  background: ${props => props.isSelected ? 'rgba(24, 144, 255, 0.05)' : 'white'};
  transition: all 0.3s ease;
  cursor: ${props => props.previewMode ? 'default' : 'pointer'};
  
  &:hover {
    border-color: ${props => props.previewMode ? 'transparent' : '#1890ff'};
    box-shadow: ${props => props.previewMode ? 'none' : '0 2px 8px rgba(24, 144, 255, 0.2)'};
    transform: ${props => props.previewMode ? 'none' : 'translateY(-1px)'};
  }
  
  ${props => props.isDragOver && `
    border-color: #52c41a !important;
    background: rgba(82, 196, 26, 0.1) !important;
    transform: scale(1.02);
  `}
`;

const ComponentControls = styled.div`
  position: absolute;
  top: -2px;
  right: -2px;
  display: flex;
  gap: 4px;
  background: rgba(255, 255, 255, 0.95);
  padding: 4px;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  opacity: ${props => props.visible ? 1 : 0};
  transform: translateY(${props => props.visible ? '0' : '-10px'});
  transition: all 0.3s ease;
  z-index: 5;
`;

const ControlButton = styled(Button)`
  width: 24px;
  height: 24px;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  box-shadow: none;
  
  &:hover {
    background: #f0f0f0;
    transform: scale(1.1);
  }
`;

const DropZone = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border: 2px dashed #d9d9d9;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.8);
  opacity: ${props => props.visible ? 1 : 0};
  pointer-events: ${props => props.visible ? 'auto' : 'none'};
  transition: all 0.3s ease;
  z-index: 1;
  
  ${props => props.isActive && `
    border-color: #52c41a;
    background: rgba(82, 196, 26, 0.1);
    
    &::before {
      content: 'Drop component here';
      color: #52c41a;
      font-weight: 600;
      font-size: 16px;
    }
  `}
`;

const Ruler = styled.div`
  position: absolute;
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid #e8e8e8;
  font-size: 10px;
  color: #666;
  z-index: 5;
  
  ${props => props.orientation === 'horizontal' && `
    top: 0;
    left: 32px;
    right: 0;
    height: 20px;
    border-bottom: 1px solid #e8e8e8;
    background-image: repeating-linear-gradient(
      to right,
      transparent,
      transparent 9px,
      #e8e8e8 9px,
      #e8e8e8 10px
    );
  `}
  
  ${props => props.orientation === 'vertical' && `
    top: 20px;
    left: 0;
    bottom: 0;
    width: 32px;
    border-right: 1px solid #e8e8e8;
    background-image: repeating-linear-gradient(
      to bottom,
      transparent,
      transparent 9px,
      #e8e8e8 9px,
      #e8e8e8 10px
    );
  `}
`;

const EnhancedPreviewArea = ({ 
  components = [], 
  onSelectComponent, 
  onDeleteComponent, 
  onUpdateComponent,
  onMoveComponent,
  previewMode = false,
  selectedComponentId,
  onDrop,
  onDragOver,
  onDragLeave
}) => {
  const [zoom, setZoom] = useState(1);
  const [showGrid, setShowGrid] = useState(true);
  const [gridSize, setGridSize] = useState(20);
  const [showRulers, setShowRulers] = useState(false);
  const [snapToGrid, setSnapToGrid] = useState(true);
  const [isDragOver, setIsDragOver] = useState(false);
  const [dragOverComponent, setDragOverComponent] = useState(null);
  const canvasRef = useRef(null);
  const [hoveredComponent, setHoveredComponent] = useState(null);

  // Handle zoom controls
  const handleZoomIn = () => setZoom(prev => Math.min(prev + 0.1, 2));
  const handleZoomOut = () => setZoom(prev => Math.max(prev - 0.1, 0.5));
  const handleZoomReset = () => setZoom(1);

  // Handle drag and drop
  const handleDragEnter = useCallback((e) => {
    e.preventDefault();
    setIsDragOver(true);
  }, []);

  const handleDragOver = useCallback((e) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = 'copy';
    if (onDragOver) onDragOver(e);
  }, [onDragOver]);

  const handleDragLeave = useCallback((e) => {
    e.preventDefault();
    if (!e.currentTarget.contains(e.relatedTarget)) {
      setIsDragOver(false);
      setDragOverComponent(null);
      if (onDragLeave) onDragLeave(e);
    }
  }, [onDragLeave]);

  const handleDrop = useCallback((e) => {
    e.preventDefault();
    setIsDragOver(false);
    setDragOverComponent(null);
    
    if (onDrop) {
      const rect = canvasRef.current?.getBoundingClientRect();
      if (rect) {
        const x = (e.clientX - rect.left) / zoom;
        const y = (e.clientY - rect.top) / zoom;
        
        // Snap to grid if enabled
        const finalX = snapToGrid ? Math.round(x / gridSize) * gridSize : x;
        const finalY = snapToGrid ? Math.round(y / gridSize) * gridSize : y;
        
        onDrop(e, { x: finalX, y: finalY });
      }
    }
  }, [onDrop, zoom, snapToGrid, gridSize]);

  // Component drag over handler
  const handleComponentDragOver = useCallback((e, componentId) => {
    e.preventDefault();
    e.stopPropagation();
    setDragOverComponent(componentId);
  }, []);

  const handleComponentDragLeave = useCallback((e, componentId) => {
    e.preventDefault();
    e.stopPropagation();
    if (!e.currentTarget.contains(e.relatedTarget)) {
      setDragOverComponent(null);
    }
  }, []);

  // Render individual component
  const renderComponent = (component) => {
    const isSelected = component.id === selectedComponentId;
    const isHovered = hoveredComponent === component.id;
    const isDraggedOver = dragOverComponent === component.id;
    
    const ComponentContent = () => {
      switch (component.type) {
        case 'text':
          return <Text>{component.props?.content || 'Sample text'}</Text>;
        case 'button':
          return (
            <Button type={component.props?.type || 'default'}>
              {component.props?.text || 'Button'}
            </Button>
          );
        case 'header':
          return (
            <Title level={component.props?.level || 2}>
              {component.props?.text || 'Header'}
            </Title>
          );
        case 'card':
          return (
            <Card title={component.props?.title || 'Card Title'}>
              {component.props?.content || 'Card content'}
            </Card>
          );
        case 'image':
          return (
            <img 
              src={component.props?.src || 'https://via.placeholder.com/150'} 
              alt={component.props?.alt || 'Image'}
              style={{ maxWidth: '100%', height: 'auto' }}
            />
          );
        case 'divider':
          return <Divider>{component.props?.text}</Divider>;
        case 'input':
          return (
            <Input 
              placeholder={component.props?.placeholder || 'Enter text'} 
              disabled={previewMode ? false : true}
            />
          );
        case 'form':
          return (
            <Form layout="vertical">
              <Form.Item label="Sample Field">
                <Input placeholder="Sample input" disabled={!previewMode} />
              </Form.Item>
            </Form>
          );
        case 'table':
          const columns = [
            { title: 'Name', dataIndex: 'name', key: 'name' },
            { title: 'Age', dataIndex: 'age', key: 'age' },
          ];
          const data = [
            { key: '1', name: 'John', age: 32 },
            { key: '2', name: 'Jane', age: 28 },
          ];
          return <Table columns={columns} dataSource={data} size="small" />;
        default:
          return (
            <div style={{ padding: '16px', border: '1px dashed #ccc', textAlign: 'center' }}>
              {component.type} Component
            </div>
          );
      }
    };

    return (
      <ComponentWrapper
        key={component.id}
        isSelected={isSelected}
        previewMode={previewMode}
        isDragOver={isDraggedOver}
        onClick={(e) => {
          e.stopPropagation();
          if (!previewMode) {
            onSelectComponent(component);
          }
        }}
        onMouseEnter={() => setHoveredComponent(component.id)}
        onMouseLeave={() => setHoveredComponent(null)}
        onDragOver={(e) => handleComponentDragOver(e, component.id)}
        onDragLeave={(e) => handleComponentDragLeave(e, component.id)}
        style={{
          padding: '16px',
          position: 'relative'
        }}
      >
        <ComponentContent />
        
        {!previewMode && (isSelected || isHovered) && (
          <ComponentControls visible={isSelected || isHovered}>
            <Tooltip title="Edit">
              <ControlButton 
                icon={<EditOutlined />} 
                size="small"
                onClick={(e) => {
                  e.stopPropagation();
                  // Handle edit
                }}
              />
            </Tooltip>
            <Tooltip title="Copy">
              <ControlButton 
                icon={<CopyOutlined />} 
                size="small"
                onClick={(e) => {
                  e.stopPropagation();
                  // Handle copy
                }}
              />
            </Tooltip>
            <Tooltip title="Move Up">
              <ControlButton 
                icon={<ArrowUpOutlined />} 
                size="small"
                onClick={(e) => {
                  e.stopPropagation();
                  if (onMoveComponent) onMoveComponent(component.id, 'up');
                }}
              />
            </Tooltip>
            <Tooltip title="Move Down">
              <ControlButton 
                icon={<ArrowDownOutlined />} 
                size="small"
                onClick={(e) => {
                  e.stopPropagation();
                  if (onMoveComponent) onMoveComponent(component.id, 'down');
                }}
              />
            </Tooltip>
            <Tooltip title="Delete">
              <ControlButton 
                icon={<DeleteOutlined />} 
                size="small"
                danger
                onClick={(e) => {
                  e.stopPropagation();
                  onDeleteComponent(component.id);
                }}
              />
            </Tooltip>
          </ComponentControls>
        )}
      </ComponentWrapper>
    );
  };

  return (
    <PreviewContainer>
      {!previewMode && (
        <PreviewToolbar>
          <Space>
            <Text strong>Canvas</Text>
            <Divider type="vertical" />
            <Tooltip title="Zoom Out">
              <Button 
                icon={<ZoomOutOutlined />} 
                size="small" 
                onClick={handleZoomOut}
                disabled={zoom <= 0.5}
              />
            </Tooltip>
            <Text style={{ minWidth: 40, textAlign: 'center' }}>
              {Math.round(zoom * 100)}%
            </Text>
            <Tooltip title="Zoom In">
              <Button 
                icon={<ZoomInOutlined />} 
                size="small" 
                onClick={handleZoomIn}
                disabled={zoom >= 2}
              />
            </Tooltip>
            <Button size="small" onClick={handleZoomReset}>
              Reset
            </Button>
          </Space>
          
          <Space>
            <Tooltip title="Toggle Grid">
              <Switch 
                checked={showGrid} 
                onChange={setShowGrid}
                checkedChildren={<BorderOutlined />}
                unCheckedChildren={<BorderOutlined />}
                size="small"
              />
            </Tooltip>
            <Tooltip title="Toggle Rulers">
              <Switch 
                checked={showRulers} 
                onChange={setShowRulers}
                size="small"
              />
            </Tooltip>
            <Tooltip title="Snap to Grid">
              <Switch 
                checked={snapToGrid} 
                onChange={setSnapToGrid}
                size="small"
              />
            </Tooltip>
          </Space>
        </PreviewToolbar>
      )}
      
      <CanvasContainer 
        showGrid={showGrid && !previewMode}
        gridSize={gridSize}
        onDragEnter={handleDragEnter}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
      >
        {showRulers && !previewMode && (
          <>
            <Ruler orientation="horizontal" />
            <Ruler orientation="vertical" />
          </>
        )}
        
        <Canvas 
          ref={canvasRef}
          zoom={zoom}
          previewMode={previewMode}
          onClick={() => onSelectComponent(null)}
        >
          {components.length > 0 ? (
            components.map(component => renderComponent(component))
          ) : (
            <Empty 
              description={
                <span>
                  No components added yet. 
                  <br />
                  {previewMode ? 'Add components to see them here.' : 'Drag components from the palette to get started.'}
                </span>
              }
              style={{ margin: '100px 0' }}
            />
          )}
        </Canvas>
        
        <DropZone visible={isDragOver && !previewMode} isActive={isDragOver} />
      </CanvasContainer>
    </PreviewContainer>
  );
};

export default EnhancedPreviewArea;
